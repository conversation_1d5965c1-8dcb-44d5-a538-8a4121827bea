from django.contrib import admin
from .models import Driver, FuelConsumption, Vehicle
from django.utils.html import format_html

class DriverAdmin(admin.ModelAdmin):
    list_display = ('name', 'vehicle')
    list_filter = ('vehicle',)
    search_fields = ('name', 'vehicle')
    list_per_page = 20

    fieldsets = (
        ('Driver Information', {
            'fields': ('name', 'vehicle')
        }),
    )

class VehicleAdmin(admin.ModelAdmin):
    list_display = ('name', 'plate_number')
    search_fields = ('name', 'plate_number')
    list_per_page = 20

class FuelConsumptionAdmin(admin.ModelAdmin):
    list_display = ('reference_number', 'date', 'driver_info', 'vehicle', 'destination_display', 
                   'purpose', 'total_liters', 'cost_display', 'number_of_trips')
    list_filter = ('date', 'vehicle', 'destination', 'purpose')
    search_fields = ('driver__name', 'reference_number', 'vehicle')
    readonly_fields = ('reference_number',)
    list_per_page = 50
    date_hierarchy = 'date'
    
    fieldsets = (
        ('Trip Information', {
            'fields': ('reference_number', 'date', 'driver', 'vehicle', 'purpose', 'destination')
        }),
        ('Fuel Details', {
            'fields': ('total_liters', 'cost', 'number_of_trips')
        })
    )
    
    def destination_display(self, obj):
        return dict(FuelConsumption.DESTINATION_CHOICES).get(obj.destination, obj.destination)
    destination_display.short_description = 'Destination'
    destination_display.admin_order_field = 'destination'
    
    def cost_display(self, obj):
        return f'₱{obj.cost:,.2f}'
    cost_display.short_description = 'Cost'
    cost_display.admin_order_field = 'cost'
    
    def driver_info(self, obj):
        return f"{obj.driver.name} ({obj.driver.vehicle})" if obj.driver else ""
    driver_info.short_description = 'Driver (Vehicle)'
    
    def save_model(self, request, obj, form, change):
        if not obj.reference_number:
            # Get the highest reference number and increment it
            last_ref = FuelConsumption.objects.order_by('-reference_number').first()
            obj.reference_number = last_ref.reference_number + 1 if last_ref else 1
        super().save_model(request, obj, form, change)

# Register models with their admin classes
admin.site.register(Driver, DriverAdmin)
admin.site.register(Vehicle, VehicleAdmin)
admin.site.register(FuelConsumption, FuelConsumptionAdmin)
