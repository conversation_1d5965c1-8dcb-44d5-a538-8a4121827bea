from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy, reverse
from django.db.models import Sum
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_GET
from .models import Driver, FuelConsumption
from .forms import FuelConsumptionForm
from datetime import date, datetime, timedelta  # Ensure timedelta is imported
import calendar
import csv
import io
from openpyxl import Workbook
from openpyxl.styles import Font, Border, Side, Alignment, PatternFill
from openpyxl.utils import get_column_letter
class DashboardView(ListView):
    template_name = 'fuel/dashboard.html'
    context_object_name = 'fuel_entries'
    
    def get_queryset(self):
        return FuelConsumption.objects.order_by('-date')[:10]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate fuel statistics
        total_consumed = FuelConsumption.objects.aggregate(
            Sum('total_liters')
        )['total_liters__sum'] or 0
        
        total_fuel = 7499.68
        remaining_fuel = total_fuel - total_consumed
        total_cost = FuelConsumption.objects.aggregate(
            Sum('cost')
        )['cost__sum'] or 0

        context.update({
            'total_consumed': round(total_consumed, 2),
            'remaining_fuel': round(remaining_fuel, 2),
            'remaining_percentage': round((remaining_fuel / total_fuel) * 100, 1),
            'total_cost': round(total_cost, 2),
            'drivers': Driver.objects.annotate(
                total_used=Sum('fuelconsumption__total_liters')
            ),
            'current_month': datetime.now().strftime('%B')
        })
        return context

class DriverListView(ListView):
    model = Driver
    template_name = 'fuel/driver_list.html'
    context_object_name = 'drivers'

class DriverDetailView(DetailView):
    model = Driver
    template_name = 'fuel/driver_detail.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        driver = self.object
        
        consumption_data = FuelConsumption.objects.filter(
            driver=driver
        ).order_by('date')
        
        total_used = consumption_data.aggregate(
            Sum('total_liters')
        )['total_liters__sum'] or 0
        
        context.update({
            'consumption_history': consumption_data,
            'total_trips': consumption_data.aggregate(
                Sum('number_of_trips')
            )['number_of_trips__sum'],
            'total_used': round(total_used, 2),
            'average_per_trip': round(total_used / sum(
                c.number_of_trips for c in consumption_data
            ), 2) if consumption_data else 0
        })
        return context

# views.py
class FuelConsumptionCreateView(CreateView):
    model = FuelConsumption
    form_class = FuelConsumptionForm
    template_name = 'fuel/fuel_form.html'

    def get_success_url(self):
        return reverse('gas_slip', kwargs={'pk': self.object.pk})
class FuelConsumptionUpdateView(UpdateView):
    model = FuelConsumption
    form_class = FuelConsumptionForm
    template_name = 'fuel/fuel_form.html'
    success_url = reverse_lazy('dashboard')

class FuelConsumptionDeleteView(DeleteView):
    model = FuelConsumption
    template_name = 'fuel/fuel_confirm_delete.html'
    success_url = reverse_lazy('dashboard')

def fuel_report(request):
    # Calculate weekly consumption
    start_date = date(2024, 10, 13)
    end_date = date(2024, 12, 31)
    
    consumption_data = FuelConsumption.objects.filter(
        date__gte=start_date,
        date__lte=end_date
    ).order_by('date')
    
    # Create weekly breakdown
    weekly_report = []
    current_week = []
    current_week_start = start_date
    
    for entry in consumption_data:
        while entry.date >= current_week_start + timedelta(days=7):  # Use timedelta here
            weekly_report.append({
                'week_start': current_week_start,
                'total_liters': sum(c.total_liters for c in current_week),
                'total_cost': sum(c.cost for c in current_week)
            })
            current_week = []
            current_week_start += timedelta(days=7)  # Use timedelta here
        
        current_week.append(entry)
    
    # Add remaining week
    if current_week:
        weekly_report.append({
            'week_start': current_week_start,
            'total_liters': sum(c.total_liters for c in current_week),
            'total_cost': sum(c.cost for c in current_week)
        })
    
    # Calculate remaining days
    today = date.today()
    remaining_days = (end_date - today).days if today < end_date else 0
    
    # Calculate average daily consumption
    total_days = (end_date - start_date).days
    days_passed = (today - start_date).days if today > start_date else 0
    avg_daily = (FuelConsumption.objects.aggregate(
        Sum('total_liters')
    )['total_liters__sum'] or 0) / days_passed if days_passed > 0 else 0
    
    context = {
        'weekly_report': weekly_report,
        'remaining_days': remaining_days,
        'avg_daily': round(avg_daily, 2),
        'projected_use': round(avg_daily * remaining_days, 2),
    }
    
    return render(request, 'fuel/fuel_report.html', context)# views.py
class DriverCreateView(CreateView):
    model = Driver
    fields = ['name']
    template_name = 'fuel/driver_form.html'
    success_url = reverse_lazy('driver_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        return response
# fuel/views.py
from django.views.generic import DetailView
from .models import FuelConsumption

class GasSlipView(DetailView):
        model = FuelConsumption
        template_name = 'fuel/gas_slip.html'
        context_object_name = 'object'

# fuel/views.py

from django.shortcuts import render
from .models import FuelConsumption

def gas_slip_print_view(request):
    slips = FuelConsumption.objects.all().order_by('date')
    context = {
        'slips': slips
    }
    return render(request, 'fuel/gas_slip_print.html', context)

from django.views.generic import CreateView, UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from .models import FuelConsumption
from .forms import FuelConsumptionForm

class FuelFormView(CreateView):
    model = FuelConsumption
    form_class = FuelConsumptionForm
    template_name = 'fuel_form.html'
    success_url = reverse_lazy('dashboard')

    PRICE_PER_LITER = 56.50  # Fixed price as in consume_fuel.py

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['office'] = 'MAYOR'
        context['title'] = 'New Fuel Entry'
        return context

    def get_initial(self):
        initial = super().get_initial()
        # Get the highest reference number and add 1 for the new entry
        last_ref = FuelConsumption.objects.order_by('-reference_number').first()
        initial['reference_number'] = (last_ref.reference_number + 1) if last_ref else 1
        return initial

    def form_valid(self, form):
        # Set the next reference number if not provided
        if not form.instance.reference_number:
            last_ref = FuelConsumption.objects.order_by('-reference_number').first()
            form.instance.reference_number = (last_ref.reference_number + 1) if last_ref else 1
        
        # Calculate cost based on liters if not provided
        if not form.instance.cost and form.instance.total_liters:
            form.instance.cost = form.instance.total_liters * self.PRICE_PER_LITER
        
        # Calculate liters based on cost if not provided
        elif not form.instance.total_liters and form.instance.cost:
            form.instance.total_liters = form.instance.cost / self.PRICE_PER_LITER

        response = super().form_valid(form)
        messages.success(self.request, 'Fuel entry created successfully.')
        return response

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)

from django.views.generic import ListView
from .models import FuelConsumption

class FuelConsumptionReportView(ListView):
    model = FuelConsumption
    template_name = 'fuel/fuel_consumption_report.html'
    context_object_name = 'entries'

    def get_queryset(self):
        # Get date range from request parameters, if provided
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        
        queryset = FuelConsumption.objects.all().order_by('date')
        
        # Filter by date range if provided
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
            
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate totals for diesel and gasoline
        diesel_total = sum(
            entry.total_liters for entry in context['entries'] 
            if entry.vehicle in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )
        
        gasoline_total = sum(
            entry.total_liters for entry in context['entries'] 
            if entry.vehicle not in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )
        
        context.update({
            'office': 'MAYOR',
            'title': 'Fuel Consumption Report',
            'diesel_total': round(diesel_total, 2),
            'gasoline_total': round(gasoline_total, 2),
            'grand_total': round(diesel_total + gasoline_total, 2),
            'report_date': date.today()
        })
        return context

def fuel_form_view(request):
    """
    Function-based view for the fuel form.
    This redirects to the class-based view.
    """
    view = FuelFormView.as_view()
    return view(request)

@require_GET
def get_destination_choices(request):
    """
    AJAX view to get destination choices based on vehicle type
    """
    vehicle = request.GET.get('vehicle', '')

    if vehicle in ['Backhoe', 'Dumptruck']:
        # Heavy equipment can only use local area
        choices = FuelConsumption.HEAVY_EQUIPMENT_DESTINATION_CHOICES
    else:
        # Ambulances use all destinations except local
        choices = FuelConsumption.AMBULANCE_DESTINATION_CHOICES

    return JsonResponse({
        'choices': [{'value': choice[0], 'label': choice[1]} for choice in choices]
    })

class DetailedFuelConsumptionReportView(ListView):
    """
    Detailed fuel consumption report view that matches the official form format
    """
    model = FuelConsumption
    template_name = 'fuel/detailed_fuel_consumption_report.html'
    context_object_name = 'entries'

    def get_queryset(self):
        # Get date range from request parameters, if provided
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        queryset = FuelConsumption.objects.all().order_by('date')

        # Filter by date range if provided
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Calculate totals for diesel and gasoline
        diesel_total = sum(
            entry.total_liters for entry in context['entries']
            if entry.vehicle in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )

        gasoline_total = sum(
            entry.total_liters for entry in context['entries']
            if entry.vehicle not in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )

        context.update({
            'office': 'MAYOR',
            'title': 'Detailed Fuel Consumption Report',
            'diesel_total': round(diesel_total, 2),
            'gasoline_total': round(gasoline_total, 2),
            'grand_total': round(diesel_total + gasoline_total, 2),
            'report_date': date.today()
        })
        return context

def fuel_consumption_form_template_view(request):
    """
    View for displaying a blank fuel consumption form template for printing
    """
    return render(request, 'fuel/fuel_consumption_form_template.html', {
        'title': 'Fuel Consumption Form Template'
    })

class ExactFuelConsumptionReportView(ListView):
    """
    Exact replica of the fuel consumption report format from the reference image
    """
    model = FuelConsumption
    template_name = 'fuel/exact_fuel_consumption_report.html'
    context_object_name = 'entries'

    def get_queryset(self):
        # Get date range from request parameters, if provided
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')

        queryset = FuelConsumption.objects.all().order_by('date')

        # Filter by date range if provided
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Calculate totals for diesel and gasoline
        diesel_total = sum(
            entry.total_liters for entry in context['entries']
            if entry.vehicle in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )

        gasoline_total = sum(
            entry.total_liters for entry in context['entries']
            if entry.vehicle not in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"]
        )

        context.update({
            'office': 'MAYOR',
            'title': 'Exact Fuel Consumption Report',
            'diesel_total': round(diesel_total, 2),
            'gasoline_total': round(gasoline_total, 2),
            'grand_total': round(diesel_total + gasoline_total, 2),
            'report_date': date.today()
        })
        return context

def exact_blank_fuel_form_view(request):
    """
    View for displaying an exact blank fuel consumption form for manual completion
    """
    return render(request, 'fuel/exact_blank_fuel_form.html', {
        'title': 'Blank Fuel Consumption Form'
    })

def export_fuel_consumption_csv(request):
    """
    Export fuel consumption data to CSV format
    """
    # Get date range from request parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    queryset = FuelConsumption.objects.all().order_by('date')

    # Filter by date range if provided
    if start_date:
        queryset = queryset.filter(date__gte=start_date)
    if end_date:
        queryset = queryset.filter(date__lte=end_date)

    # Create the HttpResponse object with CSV header
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="fuel_consumption_report.csv"'

    writer = csv.writer(response)

    # Write header rows
    writer.writerow(['FUEL CONSUMPTION REPORT'])
    writer.writerow(['Office: '])
    writer.writerow([])  # Empty row

    # Write table headers
    writer.writerow([
        'DATE', 'VEHICLE', 'PLATE #', 'ACTIVITIES', 'DESTINATION', 'DISTANCE KM',
        'AM DEPARTURE', 'AM ARRIVAL', 'PM DEPARTURE', 'PM ARRIVAL', 'TOTAL HOURS',
        'START BALANCE', 'GASOLINE', 'DIESEL', 'LUBRICANTS', 'CONSUME', 'FINISH BALANCE'
    ])

    # Write data rows
    for entry in queryset:
        plate_number = ''
        if entry.vehicle == "Ambulance L300":
            plate_number = 'L300'
        elif entry.vehicle == "Ambulance Province":
            plate_number = 'PROVINCE'
        elif entry.vehicle == "Ambulance DOH":
            plate_number = 'DOH'
        elif entry.vehicle == "Backhoe":
            plate_number = 'BACKHOE'
        elif entry.vehicle == "Dumptruck":
            plate_number = 'DUMPTRUCK'

        gasoline = entry.total_liters if entry.vehicle in ["Backhoe", "Dumptruck"] else ''
        diesel = entry.total_liters if entry.vehicle in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"] else ''

        writer.writerow([
            entry.date.strftime('%m/%d/%Y'),
            entry.vehicle.upper(),
            plate_number,
            entry.purpose.upper(),
            entry.get_destination_display().upper(),
            '',  # Distance KM - blank
            '',  # AM Departure - blank
            '',  # AM Arrival - blank
            '',  # PM Departure - blank
            '',  # PM Arrival - blank
            '',  # Total Hours - blank
            '',  # Start Balance - blank
            gasoline,
            diesel,
            '',  # Lubricants - blank
            entry.total_liters,
            ''   # Finish Balance - blank
        ])

    return response

def export_fuel_consumption_excel(request):
    """
    Export fuel consumption data to Excel format with proper formatting and borders
    """
    # Get date range from request parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    queryset = FuelConsumption.objects.all().order_by('date')

    # Filter by date range if provided
    if start_date:
        queryset = queryset.filter(date__gte=start_date)
    if end_date:
        queryset = queryset.filter(date__lte=end_date)

    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Fuel Consumption Report"

    # Define styles
    title_font = Font(name='Arial', size=14, bold=True)
    header_font = Font(name='Arial', size=10, bold=True)
    data_font = Font(name='Arial', size=9)

    # Define borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    thick_border = Border(
        left=Side(style='thick'),
        right=Side(style='thick'),
        top=Side(style='thick'),
        bottom=Side(style='thick')
    )

    # Define alignment
    center_alignment = Alignment(horizontal='center', vertical='center')
    left_alignment = Alignment(horizontal='left', vertical='center')

    # Define header fill
    header_fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')

    # Set column widths
    column_widths = [8, 12, 8, 12, 10, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

    # Add title
    ws.merge_cells('A1:Q1')
    title_cell = ws['A1']
    title_cell.value = 'FUEL CONSUMPTION REPORT'
    title_cell.font = title_font
    title_cell.alignment = center_alignment

    # Add office field
    ws.merge_cells('A3:C3')
    office_cell = ws['A3']
    office_cell.value = 'Office: ___________________'
    office_cell.font = data_font
    office_cell.alignment = left_alignment

    # Add headers - First row
    headers_row1 = ['DATE', 'VEHICLE', 'PLATE #', 'ACTIVITIES', 'DESTINATION', 'DISTANCE\nKM',
                    'AM', '', 'PM', '', 'TOTAL\nHOURS', 'START\nBALANCE', 'ADDITIONAL', '', '', 'CONSUME', 'FINISH\nBALANCE']

    for col, header in enumerate(headers_row1, 1):
        cell = ws.cell(row=5, column=col)
        cell.value = header
        cell.font = header_font
        cell.alignment = center_alignment
        cell.fill = header_fill
        cell.border = thin_border

    # Add headers - Second row
    headers_row2 = ['', '', '', '', '', '', 'DEPARTURE', 'ARRIVAL', 'DEPARTURE', 'ARRIVAL', '', '', 'GASOLINE', 'DIESEL', 'LUBRICANTS', '', '']

    for col, header in enumerate(headers_row2, 1):
        if header:  # Only add if not empty
            cell = ws.cell(row=6, column=col)
            cell.value = header
            cell.font = header_font
            cell.alignment = center_alignment
            cell.fill = header_fill
            cell.border = thin_border

    # Merge cells for main headers that span two rows
    merge_ranges = [
        'A5:A6', 'B5:B6', 'C5:C6', 'D5:D6', 'E5:E6', 'F5:F6',  # Single column headers
        'G5:H5', 'I5:J5',  # AM and PM headers
        'K5:K6', 'L5:L6',  # TOTAL HOURS and START BALANCE
        'M5:O5',  # ADDITIONAL header
        'P5:P6', 'Q5:Q6'  # CONSUME and FINISH BALANCE
    ]

    for merge_range in merge_ranges:
        ws.merge_cells(merge_range)

    # Add data rows
    row_num = 7
    for entry in queryset:
        plate_number = ''
        if entry.vehicle == "Ambulance L300":
            plate_number = 'L300'
        elif entry.vehicle == "Ambulance Province":
            plate_number = 'PROVINCE'
        elif entry.vehicle == "Ambulance DOH":
            plate_number = 'DOH'
        elif entry.vehicle == "Backhoe":
            plate_number = 'BACKHOE'
        elif entry.vehicle == "Dumptruck":
            plate_number = 'DUMPTRUCK'

        gasoline = entry.total_liters if entry.vehicle in ["Backhoe", "Dumptruck"] else ''
        diesel = entry.total_liters if entry.vehicle in ["Ambulance L300", "Ambulance Province", "Ambulance DOH"] else ''

        data_row = [
            entry.date.strftime('%m/%d/%Y'),
            entry.vehicle.upper(),
            plate_number,
            entry.purpose.upper(),
            entry.get_destination_display().upper(),
            '',  # Distance KM - blank
            '',  # AM Departure - blank
            '',  # AM Arrival - blank
            '',  # PM Departure - blank
            '',  # PM Arrival - blank
            '',  # Total Hours - blank
            '',  # Start Balance - blank
            gasoline,
            diesel,
            '',  # Lubricants - blank
            entry.total_liters,
            ''   # Finish Balance - blank
        ]

        for col, value in enumerate(data_row, 1):
            cell = ws.cell(row=row_num, column=col)
            cell.value = value
            cell.font = data_font
            cell.alignment = center_alignment if col != 4 and col != 5 else left_alignment  # Left align activities and destination
            cell.border = thin_border

        row_num += 1

    # Add empty rows for manual completion (about 20 rows)
    for i in range(20):
        for col in range(1, 18):
            cell = ws.cell(row=row_num, column=col)
            cell.value = ''
            cell.font = data_font
            cell.alignment = center_alignment
            cell.border = thin_border
        row_num += 1

    # Add signature section
    signature_row = row_num + 2
    ws.merge_cells(f'A{signature_row}:H{signature_row}')
    prep_cell = ws[f'A{signature_row}']
    prep_cell.value = 'Prepared by:'
    prep_cell.font = data_font
    prep_cell.alignment = left_alignment

    # Add signature lines
    signature_name_row = signature_row + 3
    ws.merge_cells(f'A{signature_name_row}:H{signature_name_row}')
    name_cell = ws[f'A{signature_name_row}']
    name_cell.value = 'GERLAN DORONA'
    name_cell.font = Font(name='Arial', size=10, bold=True)
    name_cell.alignment = center_alignment

    title_row = signature_name_row + 1
    ws.merge_cells(f'A{title_row}:H{title_row}')
    title_cell = ws[f'A{title_row}']
    title_cell.value = 'MDRRMO-CLERK'
    title_cell.font = data_font
    title_cell.alignment = center_alignment

    # Create response
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="fuel_consumption_report.xlsx"'

    # Save workbook to response
    wb.save(response)
    return response
