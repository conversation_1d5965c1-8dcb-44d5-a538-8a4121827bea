{% extends 'base.html' %}

{% block extra_head %}
<style>
    @page {
        size: landscape;
        margin: 0.5cm;
    }
    
    @media print {
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #FFFFFF;
            font-family: Arial, sans-serif;
            font-size: 9pt;
        }
        
        .no-print {
            display: none;
        }
        
        .report-container {
            border: 2px solid #000;
            padding: 0.3cm;
            margin: 0;
        }
        
        .report-title {
            font-size: 14pt;
            font-weight: bold;
            text-align: center;
            margin: 0.3cm 0;
            text-transform: uppercase;
        }
        
        .office-section {
            margin-bottom: 0.3cm;
            font-size: 10pt;
        }
        
        th, td {
            font-size: 7pt !important;
            padding: 2px 1px !important;
            line-height: 1.1;
        }
        
        .signature-section {
            margin-top: 0.5cm;
            font-size: 9pt;
        }
    }
    
    /* Screen and print styles */
    .report-container {
        background-color: white;
        padding: 1.5rem;
        border: 2px solid #000;
        margin: 1rem auto;
        max-width: 100%;
    }
    
    .report-title {
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
        margin: 1rem 0;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .office-section {
        margin-bottom: 1rem;
        font-weight: bold;
    }
    
    .office-line {
        border-bottom: 1px solid #000;
        display: inline-block;
        width: 200px;
        height: 20px;
        margin-left: 10px;
    }
    
    .fuel-table {
        width: 100%;
        border-collapse: collapse;
        border: 2px solid #000;
        font-size: 0.65rem;
    }
    
    .fuel-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        border: 1px solid #000;
        padding: 3px 2px;
        text-align: center;
        text-transform: uppercase;
        font-size: 0.6rem;
        line-height: 1.1;
        vertical-align: middle;
    }
    
    .fuel-table td {
        border: 1px solid #000;
        padding: 3px 2px;
        text-align: center;
        font-size: 0.65rem;
        vertical-align: middle;
        height: 20px;
    }
    
    .fuel-table tr:nth-child(even) {
        background-color: #fafafa;
    }
    
    /* Column widths to match the image */
    .col-date { width: 5%; }
    .col-vehicle { width: 8%; }
    .col-plate { width: 5%; }
    .col-activities { width: 9%; }
    .col-destination { width: 7%; }
    .col-distance { width: 4%; }
    .col-time { width: 4%; }
    .col-hours { width: 4%; }
    .col-balance { width: 4%; }
    .col-fuel { width: 4%; }
    .col-lubricant { width: 5%; }
    .col-consume { width: 4%; }
    .col-finish { width: 4%; }
    
    .signature-section {
        margin-top: 2rem;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
    
    .signature-box {
        text-align: left;
    }
    
    .signature-line {
        border-top: 1px solid #000;
        width: 180px;
        margin: 1.5rem 0 0.3rem 0;
    }
    
    .text-left { text-align: left; }
    .text-right { text-align: right; }
</style>
{% endblock %}

{% block content %}
<div class="max-w-full mx-auto p-2">
    <!-- Filter Controls (not printed) -->
    <div class="no-print mb-4 bg-gray-100 p-4 rounded">
        <form method="get" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ request.GET.start_date }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ request.GET.end_date }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Filter Report
                </button>
            </div>
            <div>
                <button type="button" onclick="window.print()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Print Report
                </button>
            </div>
            <div>
                <a href="{% url 'export_fuel_consumption_csv' %}{% if request.GET.start_date or request.GET.end_date %}?{% if request.GET.start_date %}start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}{% if request.GET.start_date %}&{% endif %}end_date={{ request.GET.end_date }}{% endif %}{% endif %}"
                   class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Export CSV
                </a>
            </div>
            <div>
                <a href="{% url 'export_fuel_consumption_excel' %}{% if request.GET.start_date or request.GET.end_date %}?{% if request.GET.start_date %}start_date={{ request.GET.start_date }}{% endif %}{% if request.GET.end_date %}{% if request.GET.start_date %}&{% endif %}end_date={{ request.GET.end_date }}{% endif %}{% endif %}"
                   class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">
                    Export Excel
                </a>
            </div>
        </form>
    </div>

    <div class="report-container">
        <!-- Report Title -->
        <div class="report-title">FUEL CONSUMPTION REPORT</div>
        
        <!-- Office Section -->
        <div class="office-section">
            Office: <span class="office-line"></span>
        </div>

        <!-- Main Table -->
        <table class="fuel-table">
            <thead>
                <tr>
                    <th rowspan="2" class="col-date">DATE</th>
                    <th rowspan="2" class="col-vehicle">VEHICLE</th>
                    <th rowspan="2" class="col-plate">PLATE #</th>
                    <th rowspan="2" class="col-activities">ACTIVITIES</th>
                    <th rowspan="2" class="col-destination">DESTINATION</th>
                    <th rowspan="2" class="col-distance">DISTANCE<br>KM</th>
                    <th colspan="2" class="col-time">AM</th>
                    <th colspan="2" class="col-time">PM</th>
                    <th rowspan="2" class="col-hours">TOTAL<br>HOURS</th>
                    <th rowspan="2" class="col-balance">START<br>BALANCE</th>
                    <th colspan="3">ADDITIONAL</th>
                    <th rowspan="2" class="col-consume">CONSUME</th>
                    <th rowspan="2" class="col-finish">FINISH<br>BALANCE</th>
                </tr>
                <tr>
                    <th class="col-time">DEPARTURE</th>
                    <th class="col-time">ARRIVAL</th>
                    <th class="col-time">DEPARTURE</th>
                    <th class="col-time">ARRIVAL</th>
                    <th class="col-fuel">GASOLINE</th>
                    <th class="col-fuel">DIESEL</th>
                    <th class="col-lubricant">LUBRICANTS</th>
                </tr>
            </thead>
            <tbody>
                {% for entry in entries %}
                <tr>
                    <td>{{ entry.date|date:"m/d/Y" }}</td>
                    <td class="text-left">{{ entry.vehicle|upper }}</td>
                    <td>
                        {% if entry.vehicle == "Ambulance L300" %}L300
                        {% elif entry.vehicle == "Ambulance Province" %}PROVINCE
                        {% elif entry.vehicle == "Ambulance DOH" %}DOH
                        {% elif entry.vehicle == "Backhoe" %}BACKHOE
                        {% elif entry.vehicle == "Dumptruck" %}DUMPTRUCK
                        {% else %}-{% endif %}
                    </td>
                    <td class="text-left">{{ entry.purpose|upper }}</td>
                    <td class="text-left">{{ entry.get_destination_display|upper }}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>
                        {% if entry.destination == "dipolog" %}7
                        {% elif entry.destination == "cagayan" %}3
                        {% elif entry.destination == "margosatubig" %}7
                        {% elif entry.destination == "pagadian_city" %}4
                        {% elif entry.destination == "ozamiz_city" %}4
                        {% elif entry.destination == "zamboanga_city" %}3
                        {% elif entry.destination == "ipil" %}4
                        {% elif entry.destination == "sindangan" %}4
                        {% else %}5{% endif %}
                    </td>
                    <td>
                        {% if entry.vehicle == "Backhoe" or entry.vehicle == "Dumptruck" %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>
                        {% if entry.vehicle == "Ambulance L300" or entry.vehicle == "Ambulance Province" or entry.vehicle == "Ambulance DOH" %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td></td>
                    <td>{{ entry.total_liters|floatformat:0 }}</td>
                    <td>
                        {% widthratio entry.total_liters 1 0.1 as balance %}
                        {{ balance|floatformat:2 }}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="17" class="text-center py-4">No fuel consumption records found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <p>Prepared by:</p>
                <div class="signature-line"></div>
                <p><strong>GERLAN DORONA</strong></p>
                <p>MDRRMO-CLERK</p>
            </div>
            <div class="signature-box">
                <!-- Empty space for noted by section -->
            </div>
        </div>
    </div>
</div>
{% endblock %}
