# Generated by Django 5.1.5 on 2025-02-17 01:59

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Driver',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('vehicle', models.CharField(max_length=100)),
            ],
            options={
                'verbose_name': 'Driver',
                'verbose_name_plural': 'Drivers',
            },
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('plate_number', models.CharField(max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='FuelConsumption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('trip_number', models.PositiveIntegerField(default=1)),
                ('number_of_trips', models.PositiveIntegerField(default=1)),
                ('purpose', models.CharField(default='Transport Patient', max_length=255)),
                ('total_liters', models.FloatField()),
                ('cost', models.FloatField()),
                ('vehicle', models.CharField(max_length=100)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel.driver')),
            ],
            options={
                'unique_together': {('driver', 'date', 'trip_number')},
            },
        ),
    ]
