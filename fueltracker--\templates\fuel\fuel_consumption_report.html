{% extends 'base.html' %}

{% block extra_head %}
<style>
    @page {
        size: landscape;
        margin: 1cm;
    }
    
    @media print {
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #FFFFFF;
            font-family: Arial, sans-serif;
        }
        
        .no-print {
            display: none;
        }
        
        .report-container {
            border: 3px solid #000;
            padding: 1cm;
            margin: 0;
        }
        
        .report-header {
            border-bottom: 2px solid #000;
            padding-bottom: 0.8cm;
            margin-bottom: 0.8cm;
            background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
        }
        
        .report-title {
            font-size: 24pt;
            font-weight: bold;
            margin: 0.8cm 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        th {
            background-color: #f0f0f0 !important;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 11pt;
        }
        
        td {
            padding: 12px 8px !important;
        }
        
        .report-footer {
            margin-top: 1.5cm;
        }
    }
    
    /* Styles for both screen and print */
    .report-container {
        background-color: white;
        padding: 3rem;
        border-radius: 0.75rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 3px solid #000;
    }
    
    .report-table {
        width: 100%;
        border-collapse: collapse;
        border: 3px solid #000;
    }
    
    .report-table th {
        background-color: #f3f4f6;
        font-weight: 600;
        border: 1.5px solid #000;
        padding: 12px 10px;
        text-align: center;
        text-transform: uppercase;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }
    
    .report-table td {
        border: 1.5px solid #000;
        padding: 10px 8px;
    }
    
    .report-table tr:hover {
        background-color: #f8f9fa;
    }
    
    .report-table tr:nth-child(even) {
        background-color: #f9fafb;
    }
    
    .header-title {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
    }
    
    .report-title {
        font-size: 2rem;
        letter-spacing: 1px;
        margin: 1.5rem 0;
        text-transform: uppercase;
        color: #1a1a1a;
    }
    
    .signature-section {
        margin-top: 2rem;
        padding-top: 1rem;
    }
    
    .signature-line {
        border-top: 1.5px solid #000;
        width: 80%;
        margin: 1.5rem auto 0.3rem;
    }

    /* Enhanced table styling for complex header */
    .report-table th {
        font-size: 0.75rem;
        padding: 8px 4px;
        vertical-align: middle;
        text-align: center;
        line-height: 1.2;
    }

    .report-table td {
        font-size: 0.8rem;
        padding: 6px 4px;
        vertical-align: middle;
        text-align: center;
    }

    /* Specific column widths for better layout */
    .w-20 { width: 5%; }
    .w-24 { width: 6%; }
    .w-32 { width: 8%; }
    .w-48 { width: 12%; }
    .w-16 { width: 4%; }
</style>
{% endblock %}

{% block content %}
<div class="max-w-full mx-auto p-6">
    <!-- Filter Controls (not printed) -->
    <div class="no-print mb-4 bg-gray-100 p-4 rounded">
        <form method="get" class="flex flex-wrap gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700">Start Date</label>
                <input type="date" name="start_date" value="{{ request.GET.start_date }}" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">End Date</label>
                <input type="date" name="end_date" value="{{ request.GET.end_date }}"
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Filter Report
                </button>
            </div>
            <div>
                <button type="button" onclick="window.print()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    Print Report
                </button>
            </div>
        </form>
    </div>

    <div class="report-container">
        <div class="report-header text-center mb-8">
            <h4 class="header-title font-semibold my-2">Republic of the Philippines</h4>
            <h4 class="header-title font-semibold my-2">PROVINCE OF ZAMBOANGA DEL SUR</h4>
            <h4 class="header-title font-semibold my-2">MUNICIPALITY OF DUMINGAG</h4>
            <h4 class="header-title font-semibold my-2">OFFICE OF THE {{ office }}</h4>
            <h2 class="report-title font-bold">FUEL CONSUMPTION REPORT</h2>
            <p class="text-sm mt-3">Report Period: 
                {% if request.GET.start_date and request.GET.end_date %}
                    {{ request.GET.start_date|date:"F d, Y" }} to {{ request.GET.end_date|date:"F d, Y" }}
                {% else %}
                    All Time
                {% endif %}
            </p>
            <p class="text-sm">Generated on: {{ report_date|date:"F d, Y" }}</p>
        </div>

        <!-- Office Field -->
        <div class="mb-4">
            <label class="font-semibold">Office: </label>
            <span class="border-b border-black inline-block w-48 text-center">{{ office }}</span>
        </div>

        <!-- Table -->
        <table class="report-table">
            <thead>
                <tr>
                    <th rowspan="2" class="w-20">DATE</th>
                    <th rowspan="2" class="w-24">VEHICLE</th>
                    <th rowspan="2" class="w-20">PLATE #</th>
                    <th rowspan="2" class="w-32">ACTIVITIES</th>
                    <th rowspan="2" class="w-32">DESTINATION</th>
                    <th rowspan="2" class="w-20">DISTANCE<br>KM</th>
                    <th colspan="2" class="w-32">AM</th>
                    <th colspan="2" class="w-32">PM</th>
                    <th rowspan="2" class="w-20">TOTAL<br>HOURS</th>
                    <th rowspan="2" class="w-20">START<br>BALANCE</th>
                    <th colspan="3" class="w-48">ADDITIONAL</th>
                    <th rowspan="2" class="w-20">CONSUME</th>
                    <th rowspan="2" class="w-20">FINISH<br>BALANCE</th>
                </tr>
                <tr>
                    <th class="w-16">DEPARTURE</th>
                    <th class="w-16">ARRIVAL</th>
                    <th class="w-16">DEPARTURE</th>
                    <th class="w-16">ARRIVAL</th>
                    <th class="w-16">GASOLINE</th>
                    <th class="w-16">DIESEL</th>
                    <th class="w-16">LUBRICANTS</th>
                </tr>
            </thead>
            <tbody>
                {% for entry in entries %}
                <tr>
                    <td class="text-center">{{ entry.date|date:"m/d/Y" }}</td>
                    <td class="text-center">{{ entry.vehicle }}</td>
                    <td class="text-center">
                        {% if entry.vehicle == "Ambulance L300" %}L300-001
                        {% elif entry.vehicle == "Ambulance Province" %}PROV-001
                        {% elif entry.vehicle == "Ambulance DOH" %}DOH-001
                        {% elif entry.vehicle == "Backhoe" %}BCK-001
                        {% elif entry.vehicle == "Dumptruck" %}DMP-001
                        {% else %}-{% endif %}
                    </td>
                    <td class="text-center">{{ entry.purpose }}</td>
                    <td class="text-center">{{ entry.get_destination_display }}</td>
                    <td class="text-center">
                        {% if entry.destination == "dipolog" %}85
                        {% elif entry.destination == "cagayan" %}120
                        {% elif entry.destination == "margosatubig" %}45
                        {% elif entry.destination == "pagadian_city" %}25
                        {% elif entry.destination == "ozamiz_city" %}65
                        {% elif entry.destination == "zamboanga_city" %}150
                        {% elif entry.destination == "ipil" %}95
                        {% elif entry.destination == "sindangan" %}35
                        {% else %}15{% endif %}
                    </td>
                    <td class="text-center">08:00</td>
                    <td class="text-center">10:00</td>
                    <td class="text-center">14:00</td>
                    <td class="text-center">16:00</td>
                    <td class="text-center">6</td>
                    <td class="text-center">100.00</td>
                    <td class="text-center">
                        {% if entry.vehicle == "Backhoe" or entry.vehicle == "Dumptruck" %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if entry.vehicle == "Ambulance L300" or entry.vehicle == "Ambulance Province" or entry.vehicle == "Ambulance DOH" %}
                            {{ entry.total_liters|floatformat:2 }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="text-center">2.5</td>
                    <td class="text-center">{{ entry.total_liters|floatformat:2 }}</td>
                    <td class="text-center">
                        {% with remaining=100|add:0 %}
                            {{ remaining|floatformat:2 }}
                        {% endwith %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="16" class="text-center py-4">No fuel consumption records found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Footer -->
        <div class="report-footer mt-8">
            <div class="grid grid-cols-2 gap-8">
                <div>
                    <p class="font-semibold">Prepared by:</p>
                    <div class="mt-16">
                        <div class="signature-line"></div>
                        <p class="font-semibold text-center">GERLAN DORONA</p>
                        <p class="text-center">MDRRMO-CLERK</p>
                    </div>
                </div>
                <div>
                    <p class="font-semibold">Noted by:</p>
                    <div class="mt-16">
                        <div class="signature-line"></div>
                        <p class="font-semibold text-center">_______________________</p>
                        <p class="text-center">Municipal Mayor</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
