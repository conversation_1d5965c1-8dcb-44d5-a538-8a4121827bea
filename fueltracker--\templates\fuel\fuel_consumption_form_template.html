{% extends 'base.html' %}

{% block extra_head %}
<style>
    @page {
        size: landscape;
        margin: 0.5cm;
    }
    
    @media print {
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #FFFFFF;
            font-family: Arial, sans-serif;
            font-size: 10pt;
        }
        
        .no-print {
            display: none;
        }
        
        .form-container {
            border: 2px solid #000;
            padding: 0.5cm;
            margin: 0;
        }
        
        .form-title {
            font-size: 16pt;
            font-weight: bold;
            margin: 0.5cm 0;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-align: center;
        }
        
        .office-field {
            text-align: left;
            margin-bottom: 0.5cm;
        }
        
        th, td {
            font-size: 8pt !important;
            padding: 3px 2px !important;
        }
        
        .signature-section {
            margin-top: 1cm;
        }
    }
    
    /* Styles for both screen and print */
    .form-container {
        background-color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 2px solid #000;
    }
    
    .form-table {
        width: 100%;
        border-collapse: collapse;
        border: 2px solid #000;
        font-size: 0.7rem;
    }
    
    .form-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        border: 1px solid #000;
        padding: 4px 2px;
        text-align: center;
        text-transform: uppercase;
        font-size: 0.65rem;
        line-height: 1.1;
        vertical-align: middle;
    }
    
    .form-table td {
        border: 1px solid #000;
        padding: 8px 4px;
        text-align: center;
        font-size: 0.7rem;
        vertical-align: middle;
        height: 30px;
    }
    
    .form-title {
        font-size: 1.5rem;
        letter-spacing: 1px;
        margin: 1rem 0;
        text-transform: uppercase;
        color: #1a1a1a;
        text-align: center;
    }
    
    .office-field {
        margin-bottom: 1rem;
        font-weight: bold;
    }
    
    .office-field input {
        border: none;
        border-bottom: 1px solid #000;
        background: transparent;
        padding: 2px 8px;
        margin-left: 10px;
        width: 200px;
    }
    
    .signature-section {
        margin-top: 2rem;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
    }
    
    .signature-box {
        text-align: center;
    }
    
    .signature-line {
        border-top: 1px solid #000;
        width: 200px;
        margin: 2rem auto 0.5rem;
    }
    
    /* Column width adjustments for better fit */
    .col-date { width: 6%; }
    .col-vehicle { width: 8%; }
    .col-plate { width: 6%; }
    .col-activities { width: 10%; }
    .col-destination { width: 8%; }
    .col-distance { width: 5%; }
    .col-time { width: 4%; }
    .col-hours { width: 5%; }
    .col-balance { width: 6%; }
    .col-fuel { width: 6%; }
    .col-lubricant { width: 6%; }
    .col-consume { width: 6%; }
    .col-finish { width: 6%; }
</style>
{% endblock %}

{% block content %}
<div class="max-w-full mx-auto p-4">
    <!-- Print Button (not printed) -->
    <div class="no-print mb-4 text-center">
        <button type="button" onclick="window.print()" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">
            Print Blank Form
        </button>
    </div>

    <div class="form-container">
        <div class="form-header">
            <h2 class="form-title">FUEL CONSUMPTION REPORT</h2>
        </div>

        <!-- Office Field -->
        <div class="office-field">
            <label>Office: </label>
            <span style="border-bottom: 1px solid #000; display: inline-block; width: 200px; height: 20px;"></span>
        </div>

        <!-- Blank Form Table -->
        <table class="form-table">
            <thead>
                <tr>
                    <th rowspan="2" class="col-date">DATE</th>
                    <th rowspan="2" class="col-vehicle">VEHICLE</th>
                    <th rowspan="2" class="col-plate">PLATE #</th>
                    <th rowspan="2" class="col-activities">ACTIVITIES</th>
                    <th rowspan="2" class="col-destination">DESTINATION</th>
                    <th rowspan="2" class="col-distance">DISTANCE<br>KM</th>
                    <th colspan="2" class="col-time">AM</th>
                    <th colspan="2" class="col-time">PM</th>
                    <th rowspan="2" class="col-hours">TOTAL<br>HOURS</th>
                    <th rowspan="2" class="col-balance">START<br>BALANCE</th>
                    <th colspan="3">ADDITIONAL</th>
                    <th rowspan="2" class="col-consume">CONSUME</th>
                    <th rowspan="2" class="col-finish">FINISH<br>BALANCE</th>
                </tr>
                <tr>
                    <th class="col-time">DEPARTURE</th>
                    <th class="col-time">ARRIVAL</th>
                    <th class="col-time">DEPARTURE</th>
                    <th class="col-time">ARRIVAL</th>
                    <th class="col-fuel">GASOLINE</th>
                    <th class="col-fuel">DIESEL</th>
                    <th class="col-lubricant">LUBRICANTS</th>
                </tr>
            </thead>
            <tbody>
                <!-- Sample filled row -->
                <tr>
                    <td>02/09/2025</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                
                <!-- Empty rows for form completion -->
                {% for i in "123456789012345678901234567890" %}
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="signature-box">
                <p>Prepared by:</p>
                <div class="signature-line"></div>
                <p><strong>GERLAN DORONA</strong></p>
                <p>MDRRMO-CLERK</p>
            </div>
            <div class="signature-box">
                <p>Noted by:</p>
                <div class="signature-line"></div>
                <p><strong>_______________________</strong></p>
                <p>Municipal Mayor</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
