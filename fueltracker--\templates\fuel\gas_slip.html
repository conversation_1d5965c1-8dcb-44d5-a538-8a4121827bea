{% extends "base.html" %}

{% block content %}
<div class="max-w-md mx-auto bg-white border border-gray-300 rounded-lg shadow-md overflow-hidden">
    <!-- Header -->
    <div class="pattern-header text-gray-800 p-4 text-center">
        <h1 class="text-2xl font-bold">FUEL SLIP</h1>
        <p class="text-sm">Ref: #{{ object.id|stringformat:"04d" }}</p>
    </div>

    <!-- Content -->
    <div class="p-6 space-y-4">
        <div class="flex justify-between items-center pattern-content p-3 rounded">
            <div>
                <p class="text-xs text-gray-700">Driver</p>
                <p class="font-medium">{{ object.driver }}</p>
            </div>
            <div class="text-right">
                <p class="text-xs text-gray-700">Date</p>
                <p>{{ object.date|date:"M d, Y" }}</p>
            </div>
        </div>

        <div class="space-y-2">
            <div class="flex justify-between border-b pb-2">
                <span class="text-gray-700 font-medium">Purpose: {{ object.purpose }}</span>
            </div>
            <div class="flex justify-between border-b pb-2">
                <span class="text-gray-700 font-medium">Destination: {{ object.get_destination_display|default:"Local" }}</span>
            </div>
            <div class="flex justify-between border-b pb-2">
                <span class="text-gray-700 font-medium">Vehicle: {{ object.vehicle }}</span>
            </div>
        </div>

        <div class="pattern-content p-4 rounded-lg">
            <div class="grid grid-cols-2 gap-4 text-center">
                <div>
                    <p class="text-sm text-gray-700">Liters Used</p>
                    <p class="text-2xl font-bold text-gray-800">{{ object.total_liters }} L</p>
                </div>
                <div>
                    <p class="text-sm text-gray-700">Total Cost</p>
                    <p class="text-2xl font-bold text-gray-800">₱{{ object.cost }}</p>
                </div>
            </div>
        </div>

        <div class="mt-6 text-center border-t pt-4">
            <p class="text-sm text-gray-700">Authorized Signature</p>
            <div class="mt-2 h-12 w-full border-b-2 border-dashed"></div>
        </div>
    </div>

    <!-- Footer -->
    <div class="pattern-footer p-3 text-center">
        <p class="text-xs text-gray-700">
            Computer generated document - Valid only on date of issue
        </p>
    </div>
</div>

<div class="mt-4 text-center space-x-3">
    <button onclick="window.print()" 
            class="bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-gray-900">
        Print Slip
    </button>
    <a href="{% url 'dashboard' %}" 
       class="bg-gray-200 px-6 py-2 rounded-lg hover:bg-gray-300">
        Return to Dashboard
    </a>
</div>

<style>
    @media print {
        body { 
            -webkit-print-color-adjust: exact; 
            margin: 0;
            padding: 0;
        }
        .no-print { display: none !important; }
    }
</style>
{% endblock %}
