from django import forms
from .models import FuelConsumption, Driver

class FuelConsumptionForm(forms.ModelForm):
    VEHICLE_CHOICES = [
        ('Ambulance L300', 'Ambulance L300'),
        ('Ambulance Province', 'Ambulance Province'),
        ('Ambulance DOH', 'Ambulance DOH'),
        ('Backhoe', 'Backhoe'),
        ('Du<PERSON>ruck', 'Dumptruck'),
    ]

    vehicle = forms.ChoiceField(choices=VEHICLE_CHOICES)
    driver = forms.ModelChoiceField(
        queryset=Driver.objects.all(),
        empty_label="Select Driver"
    )
    
    class Meta:
        model = FuelConsumption
        fields = [
            'driver',
            'date',
            'vehicle',
            'destination',
            'trip_number',
            'number_of_trips',
            'purpose',
            'total_liters',
            'cost'
        ]
        widgets = {
            'date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'form-control'
            }),
            'trip_number': forms.NumberInput(attrs={
                'min': 1,
                'class': 'form-control'
            }),
            'number_of_trips': forms.NumberInput(attrs={
                'min': 1,
                'class': 'form-control'
            }),
            'total_liters': forms.NumberInput(attrs={
                'step': '0.01',
                'class': 'form-control'
            }),
            'cost': forms.NumberInput(attrs={
                'step': '0.01',
                'class': 'form-control'
            })
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['purpose'].initial = "Transport Patient"
        self.fields['cost'].label = "Cost (₱)"
        self.fields['total_liters'].label = "Fuel Amount (Liters)"

        # Set up destination choices based on vehicle type
        # If we have an instance (editing), use its vehicle type
        # Otherwise, default to ambulance destinations
        if self.instance and hasattr(self.instance, 'vehicle'):
            vehicle = self.instance.vehicle
        else:
            # For new forms, we'll update this via JavaScript based on vehicle selection
            vehicle = None
        self._update_destination_choices(vehicle)
        # Note: You should have client-side JavaScript to listen for vehicle field changes
        # and update the destination dropdown accordingly. This is essential for UX.

    def _update_destination_choices(self, vehicle):
        """Update destination choices based on vehicle type"""
        if vehicle in ['Backhoe', 'Dumptruck']:
            # Heavy equipment can only use local area
            self.fields['destination'].choices = FuelConsumption.HEAVY_EQUIPMENT_DESTINATION_CHOICES
            self.fields['destination'].initial = 'local'
        else:
            # Ambulances use all destinations except local
            self.fields['destination'].choices = FuelConsumption.AMBULANCE_DESTINATION_CHOICES

    def clean(self):
        cleaned_data = super().clean()
        vehicle = cleaned_data.get('vehicle')
        destination = cleaned_data.get('destination')
        if vehicle in ['Backhoe', 'Dumptruck'] and destination != 'local':
            self.add_error('destination', "Heavy equipment can only use 'Local Area' as destination.")
        if vehicle in ['Ambulance L300', 'Ambulance Province', 'Ambulance DOH'] and destination == 'local':
            self.add_error('destination', "Ambulances cannot use 'Local Area' as destination.")
        return cleaned_data
